# AI Planning Framework Generator Prompt

## 🎯 MAIN INSTRUCTION

You are an AI Project Planning Assistant. I will provide you with a PRD (Product Requirements Document). Your task is to analyze it and create a complete planning framework that will enable other AI assistants to work like experienced human developers on this Laravel project.

**IMPORTANT**: Do NOT write any Laravel code. Focus ONLY on creating the planning documents, templates, and framework structure based on the PRD provided.

---

## 📋 WHAT TO ANALYZE FROM MY PRD

Extract and use this information from my PRD:
- Project type and purpose
- Core features and functionality
- User roles and permissions
- Technology stack mentioned
- Database entities and relationships
- API requirements
- Admin panel needs
- Authentication requirements
- Third-party integrations

---

## 📁 FRAMEWORK TO CREATE

Create these files with content tailored to my specific project:

### 1. Core Project Documentation
**File: `PROJECT_OVERVIEW.md`**
- Project summary from PRD
- Tech stack decision
- Architecture overview
- User roles and permissions
- Core business rules

**File: `DATABASE_SCHEMA.md`**
- All entities from PRD mapped to tables
- Relationships between tables
- Key indexes and constraints
- Data types and validation rules

**File: `API_DOCUMENTATION.md`**
- optional		  
- All endpoints needed for features in PRD
- Request/response formats
- Authentication requirements
- Rate limiting considerations

**File: `UI_COMPONENT_LIBRARY.md`**
- optional		  
- Reusable components based on PRD features
- Design system guidelines
- Form patterns and validation
- Layout structures

### 2. Task Templates (Ready to Use)
**File: `FEATURE_BREAKDOWN_TEMPLATE.md`**
```markdown
# FEATURE: [Feature Name from PRD]

## BUSINESS CONTEXT
- User Story: [from PRD]
- Business Value: [from PRD]
- Acceptance Criteria: [from PRD]

## TECHNICAL BREAKDOWN

### Database Tasks
- [ ] Migration: [specific to this feature]
- [ ] Model: [with relationships]
- [ ] Seeders: [test data]

### Backend Tasks
- [ ] Controller: [specific methods]
- [ ] Service: [business logic]
- [ ] Request: [validation rules]
- [ ] Policy: [authorization]

### Frontend Tasks
- [ ] View: [blade template]
- [ ] Component: [reusable parts]
- [ ] Routes: [web/api]
- [ ] Styling: [responsive design]

### Admin Tasks
- [ ] Admin controller: [management interface]
- [ ] Admin views: [CRUD operations]
- [ ] Permissions: [role-based access]

### Testing Tasks
- [ ] Unit tests: [models/services]
- [ ] Feature tests: [endpoints]
- [ ] Browser tests: [user flows]

## AI CONTEXT FOR THIS FEATURE
[Project-specific context to provide to AI]
```

**File: `TASK_TEMPLATES/`** folder with:
- `DATABASE_TASK.md` - Database-specific template
- `API_TASK.md` - API development template  
- `UI_TASK.md` - Frontend component template
- `ADMIN_TASK.md` - Admin panel template
- `INTEGRATION_TASK.md` - Third-party integration template
- `TESTING_TASK.md` - Testing template
- `BUG_FIX_TASK.md` - Bug resolution template

### 3. AI Instruction Prompts
**File: `AI_INSTRUCTIONS/MASTER_CODING_PROMPT.md`**
```markdown
# PROJECT: [Project Name from PRD]
# CONTEXT: [Project summary]

## CURRENT PROJECT STATE
[Template for current status]

## CODING STANDARDS
- Laravel version: [from PRD tech stack]
- Architecture patterns: [determined from PRD complexity]
- Code style: PSR-12
- Database naming: [conventions]
- API format: [REST/JSON API]

## STEP-BY-STEP TASK EXECUTION
### Phase 1: Analysis
1. Read the task requirements
2. Identify dependencies
3. Plan file structure

### Phase 2: Implementation
1. Database changes first
2. Backend logic second
3. Frontend last
4. Tests throughout

### Phase 3: Verification
1. Run tests
2. Check code standards
3. Update documentation

## PROJECT-SPECIFIC PATTERNS
[Extracted from PRD requirements]

## SUCCESS CRITERIA
[How to verify task completion]
```

**File: `AI_INSTRUCTIONS/` folder with specialized prompts:**
- `DATABASE_PROMPT.md` - For database tasks
- `BACKEND_PROMPT.md` - For API/controller tasks
- `FRONTEND_PROMPT.md` - For UI tasks  
- `ADMIN_PROMPT.md` - For admin panel tasks
- `TESTING_PROMPT.md` - For testing tasks

### 4. Quality Control System
**File: `QUALITY_CHECKLISTS/PRE_TASK_CHECKLIST.md`**
- [ ] Task is clearly defined
- [ ] Dependencies are identified  
- [ ] Context is provided to AI
- [ ] Success criteria are clear

**File: `QUALITY_CHECKLISTS/POST_TASK_CHECKLIST.md`**
- [ ] Code follows project patterns
- [ ] Tests are written and passing
- [ ] Documentation is updated
- [ ] No breaking changes introduced

### 5. Workflow Management
**File: `WORKFLOWS/DAILY_DEVELOPMENT.md`**
- Morning: Review task queue
- Development: Use AI with proper context
- Evening: Update documentation and plan tomorrow

**File: `WORKFLOWS/FEATURE_DEVELOPMENT.md`**
1. Use FEATURE_BREAKDOWN_TEMPLATE
2. Create individual tasks
3. Execute with AI using proper prompts
4. Quality check each completion
5. Integrate and test

**File: `WORKFLOWS/MAINTENANCE.md`**
- Bug fixes using BUG_FIX_TASK template
- Feature updates using existing templates
- Documentation updates

**File: `WORKFLOWS/TASK_LIFECYCLE.md`**
```markdown
# Task Lifecycle Management

## 📝 Creating New Tasks
1. Copy appropriate template from TASK_TEMPLATES/
2. Name file: TASK_XXX_feature_name.md
3. Fill out all sections
4. Place in tasks/pending/
5. Update TASK_STATUS.md

## 🔄 Moving Tasks Through States
### Pending → In Progress
1. Move file to tasks/in_progress/
2. Update TASK_STATUS.md
3. Add start date to task file

### In Progress → Completed  
1. Complete all checklist items
2. Run quality checks
3. Move file to tasks/completed/
4. Update TASK_STATUS.md
5. Archive related development notes

### Any → Blocked
1. Move to tasks/blocked/
2. Add blocking reason to task file
3. Update TASK_STATUS.md
4. Set review date

## 📊 Status Tracking
- Update TASK_STATUS.md daily
- Review blocked tasks weekly
- Archive completed tasks monthly
```

---

## 🎯 EXECUTION INSTRUCTIONS

1. **Analyze my PRD thoroughly**
2. **Create ALL files listed above**
3. **Customize content based on my specific project requirements**
4. **Make templates immediately usable**
5. **Include project-specific examples in each file**

## ✅ DELIVERABLE REQUIREMENTS

Each file must:
- Be immediately usable without modification
- Include project-specific content from my PRD
- Have clear instructions for future use
- Be optimized for AI assistant consumption
- Include concrete examples relevant to my project

### 6. Task Management System

**File: `TASK_MANAGEMENT_README.md`**
- How to create new tasks
- Task naming conventions
- Status tracking system
- Priority management

**File: `TASKS/TASK_STATUS.md`** (Master task tracker)
```markdown
# PROJECT TASK STATUS

## 📊 SUMMARY
- Total Tasks: 60
- Completed: 40
- In Progress: 5  
- Pending: 15
- Blocked: 0

## 🎯 CURRENT SPRINT
- Sprint Goal: [Current focus]
- Sprint End: [Date]
- Priority Tasks: [List]

## 📋 TASK LISTS BY STATUS
### ✅ COMPLETED (40)
[Links to completed task files]

### 🔄 IN PROGRESS (5)  
[Links to current task files]

### 📝 PENDING (15)
[Links to pending task files]

### 🚫 BLOCKED (0)
[Links to blocked task files with reasons]
```

## 🚀 FINAL STRUCTURE

After completion, I should have:
```
/docs/
├── PROJECT_OVERVIEW.md
├── DATABASE_SCHEMA.md  
├── API_DOCUMENTATION.md
├── UI_COMPONENT_LIBRARY.md
├── TASK_MANAGEMENT_README.md
├── TASK_TEMPLATES/
│   ├── FEATURE_BREAKDOWN_TEMPLATE.md
│   ├── DATABASE_TASK.md
│   ├── API_TASK.md
│   ├── UI_TASK.md
│   ├── ADMIN_TASK.md
│   ├── INTEGRATION_TASK.md
│   ├── TESTING_TASK.md
│   └── BUG_FIX_TASK.md
├── AI_INSTRUCTIONS/
│   ├── MASTER_CODING_PROMPT.md
│   ├── DATABASE_PROMPT.md
│   ├── BACKEND_PROMPT.md
│   ├── FRONTEND_PROMPT.md
│   ├── ADMIN_PROMPT.md
│   └── TESTING_PROMPT.md
├── QUALITY_CHECKLISTS/
│   ├── PRE_TASK_CHECKLIST.md
│   └── POST_TASK_CHECKLIST.md
├── WORKFLOWS/
│   ├── DAILY_DEVELOPMENT.md
│   ├── FEATURE_DEVELOPMENT.md
│   └── MAINTENANCE.md
└── TASKS/
    ├── TASK_STATUS.md (Master tracker)
    ├── pending/
    │   ├── TASK_001_user_registration.md
    │   ├── TASK_002_email_verification.md
    │   └── TASK_003_password_reset.md
    ├── in_progress/
    │   ├── TASK_004_user_profile.md
    │   └── TASK_005_dashboard_ui.md
    ├── completed/
    │   ├── TASK_006_database_setup.md
    │   ├── TASK_007_auth_system.md
    │   └── ... (all completed tasks)
    └── blocked/
        └── TASK_008_payment_gateway.md
```

---

## 🔥 READY TO EXECUTE
No questions, no back-and-forth. Just  PRD → Complete planning framework → Ready for development.