

## Project: Spiritual Learning LMS for <PERSON>

---

## 1. Project Overview

A lightweight Learning Management System (LMS) for spiritual courses.

* Built for **Sri <PERSON><PERSON><PERSON>** to host and deliver structured courses online.
* Courses consist of weekly released video lessons and written materials.
* Students enroll by paying a fee (via local Sri Lankan payment gateways).
* After enrollment, lessons unlock weekly starting from the student’s join date.
* Ad<PERSON> can manually enroll students (for offline/manual payments).
* The system is designed to be **simple, secure, and extendable**.

---

## 2. Objectives

* Provide a **clear structure** for spiritual courses (courses → lessons).
* Automate **lesson release (drip feed)** based on enrollment date.
* Enable **payments** (online/local gateway) and manual enrollments.
* Provide both **student dashboard** (progress tracking) and **admin panel** (management).
* Ensure a **clean, spiritual design** (golden yellow theme).
* Keep the system **extendable** for future features (quizzes, certificates, memberships).

---

## 3. Target Users

1. **Students**

   * Join by registering and enrolling in courses.
   * Access lessons weekly, track progress, complete lessons one by one.
   * Dashboard: see current courses, progress, profile.

2. **Admins (Guru’s team)**

   * Create and manage courses/lessons.
   * Manually enroll students if needed.
   * View student progress.
   * Manage payments and enrollment statuses.

---

## 4. Core Features

### A. Courses & Lessons

* Course contains multiple lessons.
* Each lesson includes:

  * Video link (YouTube/Vimeo/hosted)
  * Text content
  * Optional quiz (future phase)
* Lessons are released **weekly (drip feed)** after enrollment.
* Student must **mark lesson as complete** before accessing the next one.

### B. Enrollment & Payments

* Students enroll by registering, selecting a course, and paying online.
* Admin can manually enroll students (for cash/other payments).
* Each enrollment has:

  * Status: pending, active, completed, expired, cancelled
  * Start date, progress, expiry date (if course has limited access period)

### C. Student Dashboard

* Shows:

  * Active courses (with progress bar)
  * Current lesson (with “Continue” button)
  * Completed courses
  * Profile details

### D. Admin Panel

* Create/manage courses and lessons (rich text editor).
* Set drip release intervals, course price, course duration, expiry period.
* View student list, enrollments, and progress.
* Add manual enrollments.

### E. Progress Tracking

* System records when student:

  * Starts a lesson
  * Completes a lesson (via “Mark Complete” button)
* Enrollment progress updates automatically.
* Course completion when all lessons are completed.

### F. Design & Branding

* Theme: **Golden Yellow (#F6C200)** with soft white backgrounds and calm typography.
* Minimal, clean, spiritual design.
* Home page: simple course list with title, short description, price, and “Enroll” button.
---

## 5. User Journeys

### Student Flow

1. Visit home page → browse courses.
2. Click a course → see description and lesson titles (locked).
3. Click “Enroll” → login/register → input payment amount → redirect to payment gateway.
4. After successful payment, enrollment becomes **Active**.
5. Dashboard shows enrolled course → first lesson unlocked.
6. Student watches lesson video + reads content → clicks **Mark Complete**.
7. Next lesson unlocks after 7 days (or as per drip schedule).
8. Course shows as **Completed** when all lessons are finished.

### Admin Flow

1. Login → access Admin Panel.
2. Create a course: title, description, price, duration, drip settings.
3. Add lessons: title, video link, text content, drip delay.
4. Publish course.
5. Manage students: view list, manually enroll, check progress.
6. Track payments and enrollment statuses.

---

## 6. Functional Requirements

* **Authentication**: secure login/register for students and admin.
* **Course Management**: create/edit courses and lessons.
* **Lesson Drip System**: unlock lessons weekly from enrollment date.
* **Payments**: Local Payment gateway Payhere. 
* **Manual Enrollment**: admin can add students without online payment.
* **Student Progress Tracking**: per-lesson start/completion records.
* **Course Expiry**: auto-expire enrollments after set duration.
* **Dashboard**: personalized for students and admins.

---

## 7. Non-Functional Requirements

* **Security**: secure user data, prevent unauthorized access.
* **Performance**: fast load times, lightweight system.
* **Scalability**: ability to add new features (quizzes, certificates, memberships).
* **Maintainability**: clean structure for future developers.
* **Localization**: support for multiple languages later if needed.

---

## 8. Future Enhancements

* Quizzes per lesson (MCQs or text).
* Certificates upon course completion.

---

## 9. Development Phases

1. **Setup & Core Structure**

   * Install framework (Laravel + Jetstream + Livewire).
   * Configure user profiles, roles, and admin dashboard basics.
   *  make privacy,  t&c return policy etc as per payment gateway Payhere requirement. 

2. **Course & Lesson Management**

   * Admin CRUD for courses/lessons.
   * Rich text + video support.
   * Lesson drip settings.

3. **Enrollment System**

   * Student self-enrollment (pending until payment).
   * Admin manual enrollment.
   * Enrollment statuses & expiry rules. based on actual activity, no need laravel schedular. 

4. **Student Dashboard & Lesson Flow**

   * Display enrolled courses.
   * Unlock lessons weekly.
   * Mark complete button → progress tracking.

5. **Payment Gateway Integration**

   * Mock gateway: Make a mock setup for now. on a seperate page. on clikc pay it will  redurect to our page. in future we will replace it with real gateway
   * Payment flow → enrollment activation.

6. **Testing**

   * Security checks, test flows, design polish.
   * make/update readme with how to use, demo user details, install guide etc. 
   
---

## 10. Success Criteria

* Students can register, enroll, pay, and learn smoothly.
* Lessons release weekly without manual admin work.
* Admin can manage courses, lessons, students, and payments easily.
* Site looks professional, spiritual, and trustworthy.
* System is extendable for future growth.
